import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import websocket
import time
import json
from datetime import datetime
import os
import queue

# 全局变量
server_statuses = {}  # 存储服务器状态
message_queue = queue.Queue(maxsize=5000)  # 消息队列，用于线程间通信，增加队列大小到5000
stop_event = threading.Event()  # 用于停止线程的事件
threads = {}  # 存储线程对象

class ServerMonitorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("三角洲地图在线服务器监控")
        self.root.geometry("1000x600")
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 创建主框架
        self.main_frame = ttk.Frame(root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 队列满时的警告标志
        self.queue_warning_shown = False
        
        # 创建左侧服务器列表框架
        self.left_frame = ttk.LabelFrame(self.main_frame, text="服务器列表")
        self.left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建服务器列表
        self.server_tree = ttk.Treeview(self.left_frame, columns=("地址", "状态", "最后活动时间", "消息数"), show="headings")
        self.server_tree.heading("地址", text="服务器地址")
        self.server_tree.heading("状态", text="状态")
        self.server_tree.heading("最后活动时间", text="最后活动时间")
        self.server_tree.heading("消息数", text="消息数")
        self.server_tree.column("地址", width=150)
        self.server_tree.column("状态", width=80)
        self.server_tree.column("最后活动时间", width=150)
        self.server_tree.column("消息数", width=80)
        self.server_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.left_frame, orient=tk.VERTICAL, command=self.server_tree.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.server_tree.configure(yscrollcommand=scrollbar.set)
        
        # 绑定双击事件用于复制服务器地址
        self.server_tree.bind("<Double-1>", self.copy_server_address)
        
        # 创建右侧消息和控制框架
        self.right_frame = ttk.Frame(self.main_frame)
        self.right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建消息显示区域
        self.log_frame = ttk.LabelFrame(self.right_frame, text="消息日志")
        self.log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 优化滚动文本框的性能
        self.log_text = scrolledtext.ScrolledText(
            self.log_frame,
            wrap=tk.WORD,
            state=tk.NORMAL,
            height=20,  # 限制高度
            width=60    # 限制宽度
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 配置文本框以提高性能
        self.log_text.configure(undo=False)  # 禁用撤销功能以提高性能
        
        # 创建控制区域
        self.control_frame = ttk.LabelFrame(self.right_frame, text="控制面板")
        self.control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 添加控制按钮
        self.refresh_btn = ttk.Button(self.control_frame, text="刷新服务器列表", command=self.refresh_servers)
        self.refresh_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.toggle_btn = ttk.Button(self.control_frame, text="启用/禁用选中服务器", command=self.toggle_server)
        self.toggle_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.clear_log_btn = ttk.Button(self.control_frame, text="清除日志", command=self.clear_log)
        self.clear_log_btn.pack(side=tk.LEFT, padx=5, pady=5)

        self.sort_by_messages_btn = ttk.Button(self.control_frame, text="按消息数排序", command=self.sort_by_message_count)
        self.sort_by_messages_btn.pack(side=tk.LEFT, padx=5, pady=5)

        self.pause_all_btn = ttk.Button(self.control_frame, text="暂停所有", command=self.pause_all_servers)
        self.pause_all_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 绑定服务器选择事件
        self.server_tree.bind("<ButtonRelease-1>", self.on_server_select)
        
        # 日志限制
        self.max_log_lines = 800  # 减少最大日志行数以提高性能
        self.log_count = 0

        # 状态更新优化
        self.last_status_update = {}  # 记录每个服务器的最后更新时间
        self.status_update_interval = 2  # 状态更新间隔（秒）
        
        # 初始化数据
        self.load_servers()
        
        # 启动UI更新线程
        self.update_thread = threading.Thread(target=self.update_ui, daemon=True)
        self.update_thread.start()
        
        # 定期刷新UI
        self.schedule_ui_update()
    
    def load_servers(self):
        """从server.txt加载服务器列表"""
        # 优先使用活跃的服务器
        if os.path.exists("active_servers.txt"):
            with open("active_servers.txt", "r", encoding="utf-8") as f:
                servers = [line.strip() for line in f.readlines() if line.strip()]
            self.log_message(f"从active_servers.txt中读取到 {len(servers)} 个活跃服务器")
        elif os.path.exists("websocket_working_servers.txt"):
            with open("websocket_working_servers.txt", "r", encoding="utf-8") as f:
                servers = [line.strip() for line in f.readlines() if line.strip()]
            self.log_message(f"从websocket_working_servers.txt中读取到 {len(servers)} 个已验证的服务器")
        elif os.path.exists("available_servers.txt"):
            with open("available_servers.txt", "r", encoding="utf-8") as f:
                servers = [line.strip() for line in f.readlines() if line.strip()]
            self.log_message(f"从available_servers.txt中读取到 {len(servers)} 个可用服务器")
        elif os.path.exists("server.txt"):
            with open("server.txt", "r", encoding="utf-8") as f:
                servers = [line.strip() for line in f.readlines() if line.strip()]
            self.log_message(f"从server.txt中读取到 {len(servers)} 个服务器")
        else:
            self.log_message("没有找到服务器列表文件，请先运行main.py生成服务器列表")
            return
        
        if not servers:
            self.log_message("没有找到可用的服务器")
            return
        
        self.log_message(f"从server.txt中读取到 {len(servers)} 个服务器")
        
        # 清空树视图
        for item in self.server_tree.get_children():
            self.server_tree.delete(item)
        
        # 添加服务器到树视图和状态字典
        for server in servers:
            if server not in server_statuses:
                server_statuses[server] = {
                    "status": "未连接",
                    "last_activity": "-",
                    "message_count": 0,
                    "enabled": True
                }
            
            self.server_tree.insert("", tk.END, server, values=(
                server,
                server_statuses[server]["status"],
                server_statuses[server]["last_activity"],
                server_statuses[server]["message_count"]
            ))
        
        # 启动监控线程
        self.start_monitoring()
    
    def start_monitoring(self):
        """为每个启用的服务器启动监控线程"""
        for server, status in server_statuses.items():
            if status["enabled"] and server not in threads:
                thread = threading.Thread(target=self.monitor_server, args=(server,), daemon=True)
                threads[server] = thread
                thread.start()
                self.log_message(f"已启动线程监控服务器: {server}")
    
    def safe_put_message(self, msg_type, data):
        """安全地将消息放入队列，避免队列满时阻塞"""
        try:
            # 非阻塞方式放入队列，如果队列满则丢弃消息
            message_queue.put_nowait((msg_type, data))
            self.queue_warning_shown = False
        except queue.Full:
            # 队列满了，先清理一些旧消息，然后尝试放入新消息
            try:
                # 清理队列中的一些旧消息（清理队列的1/4）
                clear_count = message_queue.qsize() // 4
                for _ in range(clear_count):
                    try:
                        message_queue.get_nowait()
                    except queue.Empty:
                        break
                # 再次尝试放入新消息
                message_queue.put_nowait((msg_type, data))
                if not self.queue_warning_shown:
                    print("警告: 消息队列已满，已清理部分旧消息")
                    self.queue_warning_shown = True
            except queue.Full:
                # 如果还是满，则丢弃消息
                if not self.queue_warning_shown:
                    print("警告: 消息队列已满，部分消息被丢弃")
                    self.queue_warning_shown = True
    
    def monitor_server(self, server):
        """监控指定的WebSocket服务器"""
        while not stop_event.is_set() and server_statuses[server]["enabled"]:
            try:
                # 更新状态为"连接中"
                server_statuses[server]["status"] = "连接中"
                self.safe_put_message("update_status", server)

                # 先尝试WS连接
                try:
                    # 确保server是字符串格式
                    server_str = str(server).strip()
                    if ":" in server_str:
                        host = server_str.split(":")[0]
                        port = server_str.split(":")[1]
                    else:
                        host = server_str
                        port = "9000"  # 默认端口

                    ws_url = f"ws://{host}:{port}"
                    self.safe_put_message("log", f"尝试WS连接: {ws_url}")

                    # 创建WebSocket连接，确保所有参数都是正确的类型
                    ws = websocket.WebSocketApp(
                        ws_url,
                        on_open=lambda ws, server=server: self.on_open(ws, server),
                        on_message=lambda ws, msg, server=server: self.on_message(ws, msg, server),
                        on_error=lambda ws, err, server=server: self.on_error(ws, err, server),
                        on_close=lambda ws, code=None, msg=None, server=server: self.on_close(ws, code, msg, server)
                    )
                    # 设置连接超时和ping间隔
                    ws.run_forever(
                        ping_interval=60,  # 每60秒发送ping
                        ping_timeout=20   # ping超时20秒
                    )
                except Exception as e:
                    error_msg = str(e)
                    self.safe_put_message("log", f"WS连接到 {server} 失败: {error_msg}")

                    # 如果WS连接失败，尝试WSS连接
                    try:
                        server_str = str(server).strip()
                        ws_url = f"wss://{server_str}"
                        self.safe_put_message("log", f"尝试WSS连接: {ws_url}")

                        ws = websocket.WebSocketApp(
                            ws_url,
                            on_open=lambda ws, server=server: self.on_open(ws, server),
                            on_message=lambda ws, msg, server=server: self.on_message(ws, msg, server),
                            on_error=lambda ws, err, server=server: self.on_error(ws, err, server),
                            on_close=lambda ws, code=None, msg=None, server=server: self.on_close(ws, code, msg, server)
                        )
                        # 设置SSL选项和连接超时
                        ws.run_forever(
                            sslopt={"cert_reqs": 0},
                            ping_interval=60,  # 每60秒发送ping
                            ping_timeout=20   # ping超时20秒
                        )
                    except Exception as e:
                        error_msg = str(e)
                        self.safe_put_message("log", f"WSS连接到 {server} 也失败: {error_msg}")
                        server_statuses[server]["status"] = "连接失败"
                        self.safe_put_message("update_status", server)
            except Exception as e:
                error_msg = str(e)
                self.safe_put_message("log", f"监控服务器 {server} 时发生错误: {error_msg}")
                server_statuses[server]["status"] = "错误"
                self.safe_put_message("update_status", server)

            # 如果连接断开或失败，等待后重试
            if server_statuses[server]["enabled"]:
                if server_statuses[server]["status"] == "连接失败":
                    self.safe_put_message("log", f"服务器 {server} 连接失败，10秒后重试...")
                    time.sleep(10)
                else:
                    self.safe_put_message("log", f"服务器 {server} 连接断开，5秒后重试...")
                    time.sleep(5)
            else:
                break  # 如果被禁用，退出监控循环
    
    def on_open(self, ws, server):
        """连接建立时的回调函数"""
        _ = ws  # 标记参数已使用
        self.safe_put_message("log", f"成功连接到 {server}")
        server_statuses[server]["status"] = "已连接"
        server_statuses[server]["last_activity"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.safe_put_message("update_status", server)
    
    def on_message(self, ws, message, server):
        """收到消息时的回调函数"""
        try:
            # 确保消息是字符串格式
            if isinstance(message, bytes):
                message = message.decode('utf-8', errors='ignore')
            elif not isinstance(message, str):
                message = str(message)

            server_url = str(ws.url) if hasattr(ws, 'url') else str(server)

            # 限制消息长度，避免日志过长
            log_message = message[:50] + "..." if len(message) > 50 else message
            self.safe_put_message("log", f"从 {server} 收到消息: {log_message}")

            # 更新服务器状态
            server_statuses[server]["status"] = "活动"
            server_statuses[server]["last_activity"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            server_statuses[server]["message_count"] += 1
            self.safe_put_message("update_status", server)

            # 解析URL获取IP地址
            if server_url.startswith('ws://'):
                ip = server_url[5:].split('/')[0]
            elif server_url.startswith('wss://'):
                ip = server_url[6:].split('/')[0]
            else:
                ip = str(server).split(':')[0] if ':' in str(server) else str(server)

            # 创建check_info.txt并写入信息（减少文件写入频率）
            try:
                # 只在重要消息时写入文件，减少I/O操作
                if server_statuses[server]["message_count"] % 10 == 1:  # 每10条消息写入一次
                    threading.Thread(
                        target=self.write_message_to_file,
                        args=(ip, message),
                        daemon=True
                    ).start()
            except Exception as e:
                error_msg = str(e)
                self.safe_put_message("log", f"写入消息到文件时出错: {error_msg}")
        except Exception as e:
            error_msg = str(e)
            self.safe_put_message("log", f"处理消息时出错: {error_msg}")
    
    def write_message_to_file(self, ip, message):
        """将消息写入文件"""
        try:
            with open("check_info.txt", "a", encoding="utf-8") as f:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"[{timestamp}] 收到来自 {ip} 的消息\n")
                f.write(f"消息内容: {message[:200]}...\n\n")  # 只记录消息的前200个字符
        except Exception as e:
            print(f"写入文件错误: {e}")
    
    def on_error(self, ws, error, server):
        """发生错误时的回调函数"""
        try:
            _ = ws  # 标记参数已使用
            # 确保错误信息是字符串格式
            error_msg = str(error) if error else "未知错误"
            self.safe_put_message("log", f"连接 {server} 时出错: {error_msg}")
            server_statuses[server]["status"] = "错误"
            self.safe_put_message("update_status", server)
        except Exception as e:
            print(f"处理错误回调时出错: {e}")

    def on_close(self, ws, close_status_code, close_msg, server):
        """连接关闭时的回调函数"""
        try:
            _ = ws  # 标记参数已使用
            # 构建关闭信息
            close_info = f"连接到 {server} 已关闭"
            if close_status_code:
                close_info += f" (状态码: {close_status_code})"
            if close_msg:
                close_info += f" (消息: {str(close_msg)[:50]})"

            self.safe_put_message("log", close_info)

            if server_statuses[server]["enabled"]:
                server_statuses[server]["status"] = "已断开"
            else:
                server_statuses[server]["status"] = "已禁用"
            self.safe_put_message("update_status", server)
        except Exception as e:
            print(f"处理关闭回调时出错: {e}")
    
    def update_ui(self):
        """更新UI的线程函数，只处理队列中的消息，不直接更新UI"""
        while not stop_event.is_set():
            try:
                # 只从队列获取消息，但不直接更新UI
                # UI更新由schedule_ui_update方法在主线程中处理
                time.sleep(0.1)  # 短暂休眠以减少CPU使用
            except Exception as e:
                print(f"UI更新线程错误: {e}")
    
    def schedule_ui_update(self):
        """定期更新UI，确保界面响应"""
        # 处理队列中的消息，但限制每次处理的数量和时间
        start_time = time.time()
        max_process_time = 0.02  # 限制每次处理时间为20毫秒，避免阻塞UI

        try:
            # 根据队列大小动态调整处理数量，但限制最大处理数量
            queue_size = message_queue.qsize()
            if queue_size > 100:
                # 队列较大时，处理更多消息，但不超过30个
                process_count = min(30, queue_size)
            elif queue_size > 20:
                process_count = min(15, queue_size)
            else:
                process_count = min(8, queue_size)

            # 处理消息，但限制处理时间
            processed = 0
            for _ in range(process_count):
                if message_queue.empty():
                    break

                # 检查处理时间，避免长时间阻塞UI
                if time.time() - start_time > max_process_time:
                    break

                try:
                    msg_type, data = message_queue.get_nowait()

                    if msg_type == "log":
                        self.log_message(data)
                    elif msg_type == "update_status":
                        self.update_server_status(data)

                    processed += 1
                except queue.Empty:
                    break

            # 如果队列仍然很大，显示警告
            if message_queue.qsize() > 1000:
                if not self.queue_warning_shown:
                    print(f"警告: 消息队列积压严重 ({message_queue.qsize()} 条消息)")
                    self.queue_warning_shown = True
            else:
                self.queue_warning_shown = False

        except Exception as e:
            print(f"处理消息队列错误: {e}")

        # 动态调整更新频率，减少不必要的更新
        queue_size = message_queue.qsize()
        if queue_size > 50:
            # 队列较大时，更频繁地更新，但不要太频繁
            update_interval = 80
        elif queue_size > 10:
            update_interval = 120
        else:
            # 队列较小时，降低更新频率
            update_interval = 200

        self.root.after(update_interval, self.schedule_ui_update)
    
    def log_message(self, message):
        """向日志文本框添加消息，并限制日志行数"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 批量处理日志，减少UI更新频率
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_count += 1

        # 如果日志行数超过限制，批量删除旧的日志
        if self.log_count > self.max_log_lines:
            # 一次性删除多行，减少UI操作次数
            lines_to_delete = self.log_count - self.max_log_lines + 100  # 多删除一些，减少频繁删除
            self.log_text.delete(1.0, f"{lines_to_delete}.0")
            self.log_count = self.max_log_lines - 100

        # 减少滚动频率，只在必要时滚动
        if self.log_count % 5 == 0:  # 每5条消息滚动一次
            self.log_text.see(tk.END)
    
    def update_server_status(self, server):
        """更新服务器状态显示，带频率限制"""
        try:
            current_time = time.time()

            # 检查是否需要限制更新频率（除了重要状态变化）
            if server in self.last_status_update:
                time_since_last = current_time - self.last_status_update[server]
                status = server_statuses[server]["status"]

                # 对于频繁的"活动"状态更新，限制频率
                if status == "活动" and time_since_last < self.status_update_interval:
                    return

            self.last_status_update[server] = current_time

            status = server_statuses[server]
            # 更新树视图中的状态
            self.server_tree.item(server, values=(
                server,
                status["status"],
                status["last_activity"],
                status["message_count"]
            ))

            # 根据状态设置颜色
            if status["status"] == "活动":
                self.server_tree.item(server, tags=("active",))
            elif status["status"] == "已连接":
                self.server_tree.item(server, tags=("connected",))
            elif status["status"] == "连接中":
                self.server_tree.item(server, tags=("connecting",))
            elif status["status"] == "已禁用":
                self.server_tree.item(server, tags=("disabled",))
            else:
                self.server_tree.item(server, tags=("error",))

            # 配置标签颜色（只在第一次配置）
            if not hasattr(self, '_tags_configured'):
                self.server_tree.tag_configure("active", background="#a3f7bf")  # 浅绿色
                self.server_tree.tag_configure("connected", background="#c4e0f9")  # 浅蓝色
                self.server_tree.tag_configure("connecting", background="#faf0ca")  # 浅黄色
                self.server_tree.tag_configure("disabled", background="#e0e0e0")  # 灰色
                self.server_tree.tag_configure("error", background="#ffcccb")  # 浅红色
                self._tags_configured = True

        except Exception as e:
            print(f"更新服务器状态错误: {e}")
    
    def on_server_select(self, event):
        """当选择服务器时显示详细信息"""
        _ = event  # 标记参数已使用
        selected_items = self.server_tree.selection()
        if selected_items:
            server = selected_items[0]
            status = server_statuses[server]
            self.log_message(f"服务器: {server}")
            self.log_message(f"状态: {status['status']}")
            self.log_message(f"最后活动时间: {status['last_activity']}")
            self.log_message(f"接收消息数: {status['message_count']}")
            self.log_message(f"监控状态: {'启用' if status['enabled'] else '禁用'}")
    
    def toggle_server(self):
        """启用或禁用选中的服务器监控"""
        selected_items = self.server_tree.selection()
        if not selected_items:
            messagebox.showinfo("提示", "请先选择一个服务器")
            return
        
        server = selected_items[0]
        if server_statuses[server]["enabled"]:
            # 禁用服务器
            server_statuses[server]["enabled"] = False
            server_statuses[server]["status"] = "已禁用"
            self.log_message(f"已禁用服务器 {server} 的监控")
            # 线程会自行结束
        else:
            # 启用服务器
            server_statuses[server]["enabled"] = True
            server_statuses[server]["status"] = "未连接"
            self.log_message(f"已启用服务器 {server} 的监控")
            # 启动新的监控线程
            if server not in threads or not threads[server].is_alive():
                thread = threading.Thread(target=self.monitor_server, args=(server,), daemon=True)
                threads[server] = thread
                thread.start()
        
        self.update_server_status(server)
    
    def refresh_servers(self):
        """刷新服务器列表"""
        self.load_servers()
    
    def clear_log(self):
        """清除日志文本框"""
        self.log_text.delete(1.0, tk.END)

    def sort_by_message_count(self):
        """按接收消息数从多到少排序服务器列表"""
        try:
            # 获取所有服务器及其消息数
            server_data = []
            for server, status in server_statuses.items():
                server_data.append((server, status["message_count"], status))

            # 按消息数从多到少排序
            server_data.sort(key=lambda x: x[1], reverse=True)

            # 清空树视图
            for item in self.server_tree.get_children():
                self.server_tree.delete(item)

            # 按排序后的顺序重新添加服务器
            for server, _, status in server_data:
                self.server_tree.insert("", tk.END, server, values=(
                    server,
                    status["status"],
                    status["last_activity"],
                    status["message_count"]
                ))

                # 重新设置颜色标签
                if status["status"] == "活动":
                    self.server_tree.item(server, tags=("active",))
                elif status["status"] == "已连接":
                    self.server_tree.item(server, tags=("connected",))
                elif status["status"] == "连接中":
                    self.server_tree.item(server, tags=("connecting",))
                elif status["status"] == "已禁用":
                    self.server_tree.item(server, tags=("disabled",))
                else:
                    self.server_tree.item(server, tags=("error",))

            self.log_message(f"已按消息数排序，共 {len(server_data)} 个服务器")

        except Exception as e:
            error_msg = str(e)
            self.log_message(f"排序时发生错误: {error_msg}")
            messagebox.showerror("错误", f"排序失败: {error_msg}")

    def pause_all_servers(self):
        """暂停所有服务器的监控"""
        try:
            paused_count = 0
            enabled_count = 0

            # 统计当前启用的服务器数量
            for server, status in server_statuses.items():
                if status["enabled"]:
                    enabled_count += 1

            if enabled_count == 0:
                messagebox.showinfo("提示", "当前没有启用的服务器")
                return

            # 确认操作
            if messagebox.askyesno("确认", f"确定要暂停所有 {enabled_count} 个启用的服务器吗？"):
                # 暂停所有启用的服务器
                for server, status in server_statuses.items():
                    if status["enabled"]:
                        status["enabled"] = False
                        status["status"] = "已禁用"
                        paused_count += 1
                        # 更新UI显示
                        self.update_server_status(server)

                self.log_message(f"已暂停 {paused_count} 个服务器的监控")
                messagebox.showinfo("完成", f"已成功暂停 {paused_count} 个服务器的监控")

        except Exception as e:
            error_msg = str(e)
            self.log_message(f"暂停服务器时发生错误: {error_msg}")
            messagebox.showerror("错误", f"暂停失败: {error_msg}")
    
    def on_closing(self):
        """关闭窗口时的处理"""
        if messagebox.askokcancel("退出", "确定要退出吗?"):
            stop_event.set()  # 设置停止事件
            self.root.destroy()

    def copy_server_address(self, event):
        """双击复制服务器地址"""
        _ = event  # 标记参数已使用
        selected_items = self.server_tree.selection()
        if selected_items:
            server = selected_items[0]
            self.root.clipboard_clear()
            self.root.clipboard_append(server)
            self.root.update()
            self.log_message(f"已复制服务器地址: {server}")
            #messagebox.showinfo("复制成功", f"已将服务器地址 {server} 复制到剪贴板")

def main():
    """主函数"""
    # 设置websocket日志级别
    websocket.enableTrace(False)
    
    # 创建主窗口
    root = tk.Tk()
    app = ServerMonitorGUI(root)
    _ = app  # 标记变量已使用，避免垃圾回收

    # 运行主循环
    root.mainloop()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"程序发生异常: {e}")
        # 将异常写入日志
        with open("check_info.txt", "a", encoding="utf-8") as f:
            f.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 程序异常: {e}\n") 